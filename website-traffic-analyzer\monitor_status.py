#!/usr/bin/env python3
"""
监控页面状态检查器
"""
import requests
import json
from datetime import datetime

def check_monitor_status():
    try:
        print("🔍 监控页面状态检查")
        print("=" * 60)
        
        # 获取API状态
        response = requests.get('http://localhost:5000/api/status')
        data = response.json()
        
        # 基本统计
        print(f"📊 处理统计:")
        print(f"   总处理域名: {data['totalProcessed']:,}")
        print(f"   成功率: {data['successRate']:.1f}%")
        print(f"   流量发现率: {data['trafficRate']:.1f}%")
        print(f"   日期发现率: {data['dateRate']:.1f}%")
        print(f"   2025年域名: {len(data['domains2025'])} 个")
        
        # 最新更新时间
        last_update = datetime.fromisoformat(data['lastUpdate'].replace('Z', '+00:00'))
        print(f"   最后更新: {last_update.strftime('%H:%M:%S')}")
        
        print(f"\n🆕 2025年域名排行榜 (前10名):")
        print("-" * 60)
        
        # 按访问量排序显示
        domains = sorted(data['domains2025'], 
                        key=lambda x: x.get('trafficNumber', 0), 
                        reverse=True)
        
        for i, domain in enumerate(domains[:10], 1):
            name = domain.get('domain', 'unknown')
            visits = domain.get('totalVisits', 'N/A')
            creation = domain.get('domainCreation', 'N/A')
            change = domain.get('visitsChange', 'N/A')
            index = domain.get('index', 0)
            
            print(f"{i:2d}. {name:<25} {visits:>10} {creation:>12} {change:>8} #{index:,}")
        
        print(f"\n📁 数据文件:")
        print("-" * 60)
        files = data.get('files', [])[:5]  # 显示最新5个文件
        for file_info in files:
            name = file_info.get('name', 'unknown')
            processed = file_info.get('processed', 0)
            size = file_info.get('size', 0)
            modified = file_info.get('modified', '')
            
            if modified:
                mod_time = datetime.fromisoformat(modified).strftime('%H:%M:%S')
            else:
                mod_time = 'unknown'
            
            print(f"   {name:<35} {processed:>6} 域名 {size:>8} 字节 {mod_time}")
        
        print(f"\n🌐 监控页面: http://localhost:5000")
        print(f"🔄 API状态: http://localhost:5000/api/status")
        print(f"📱 页面已在浏览器中打开，请刷新查看最新数据")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到监控服务器")
        print("   请确保服务器正在运行: python real-time-server.py")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == '__main__':
    check_monitor_status()
