#!/usr/bin/env python3
"""
实时监控服务器 - 从主目录运行，显示真实数据
"""

from flask import Flask, jsonify, send_file
from flask_cors import CORS
import glob
import json
import os
from datetime import datetime
import threading
import time

app = Flask(__name__)
CORS(app)

class RealDataCollector:
    def __init__(self):
        self.latest_data = {
            'totalProcessed': 0,
            'successCount': 0,
            'trafficFoundCount': 0,
            'dateFoundCount': 0,
            'successRate': 0,
            'trafficRate': 0,
            'dateRate': 0,
            'domains2025': [],
            'lastUpdate': datetime.now().isoformat(),
            'files': []
        }
        self.update_data()
        
        # 启动后台更新线程
        self.update_thread = threading.Thread(target=self.continuous_update, daemon=True)
        self.update_thread.start()
    
    def is_2025_domain(self, date_str):
        """检查是否是2025年注册的域名"""
        if not date_str:
            return False
        try:
            year = int(date_str.split('-')[0])
            return year == 2025
        except:
            return False
    
    def convert_traffic_to_number(self, traffic_str):
        """将流量字符串转换为数字"""
        if not traffic_str:
            return 0
        
        traffic_str = str(traffic_str).upper()
        
        try:
            if traffic_str.endswith('K'):
                return float(traffic_str[:-1]) * 1000
            elif traffic_str.endswith('M'):
                return float(traffic_str[:-1]) * 1000000
            elif traffic_str.endswith('B'):
                return float(traffic_str[:-1]) * 1000000000
            else:
                return float(traffic_str.replace(',', ''))
        except:
            return 0
    
    def is_valid_traffic_data(self, traffic_str):
        """验证流量数据是否有效"""
        if not traffic_str:
            return False
        
        # 排除明显错误的数据
        if traffic_str in ['0', '9b', '9B', '0K', '0M', '0B']:
            return False
        
        # 检查是否是单独的整数+单位（可能是错误的）
        import re
        if re.match(r'^[0-9][bBmMkK]$', traffic_str):
            return False
        
        # 包含小数点的更可能是真实数据
        if '.' in traffic_str:
            return True
        
        # 检查数字是否合理
        try:
            number_part = re.findall(r'([0-9]+)', traffic_str)[0]
            number = int(number_part)
            
            # 如果是整数且小于等于9，可能是错误数据
            if number <= 9:
                return False
            
            return True
        except:
            return False
    
    def update_data(self):
        """更新数据"""
        try:
            # 查找所有结果文件
            patterns = [
                'fixed_traffic_*.json',
                'stable_batch_*.json',
                '*traffic*.json',
                'test_*.json',
                '*.json'
            ]
            
            result_files = []
            for pattern in patterns:
                files = glob.glob(pattern)
                result_files.extend(files)
            
            # 去重
            result_files = list(set(result_files))
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 找到 {len(result_files)} 个数据文件")

            total_processed = 0
            total_success = 0
            total_traffic = 0
            total_dates = 0
            new_2025_domains = []
            file_info = []

            debug_2025_count = 0
            
            for file in result_files:
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    total_processed += data.get('total_processed', 0)
                    total_success += data.get('success_count', 0)
                    total_traffic += data.get('traffic_found_count', 0)
                    total_dates += data.get('date_found_count', 0)
                    
                    # 收集2025年域名
                    if 'new_2025_domains' in data:
                        file_2025_domains = data['new_2025_domains']
                        print(f"  文件 {file} 包含 {len(file_2025_domains)} 个2025年域名")
                        for domain_info in file_2025_domains:
                            # 确保数据完整性
                            if isinstance(domain_info, dict) and 'domain' in domain_info:
                                # 标准化字段名
                                visits_change = domain_info.get('visitsChange', domain_info.get('visits_change', 'N/A'))
                                if visits_change is None or visits_change == 'None' or visits_change == '':
                                    visits_change = 'N/A'

                                total_visits = domain_info.get('totalVisits', domain_info.get('total_visits', 'N/A'))
                                if total_visits is None or total_visits == 'None' or total_visits == '':
                                    total_visits = 'N/A'

                                domain_creation = domain_info.get('domainCreation', domain_info.get('domain_creation', 'N/A'))
                                if domain_creation is None or domain_creation == 'None' or domain_creation == '':
                                    domain_creation = 'N/A'

                                standardized_domain = {
                                    'domain': domain_info.get('domain', 'unknown'),
                                    'totalVisits': total_visits,
                                    'trafficNumber': domain_info.get('trafficNumber', 0),
                                    'domainCreation': domain_creation,
                                    'visitsChange': visits_change,
                                    'index': domain_info.get('index', 0)
                                }
                                new_2025_domains.append(standardized_domain)
                                debug_2025_count += 1
                    
                    # 从results中查找2025年域名
                    results = data.get('results', [])
                    for result in results:
                        if result.get('status') == 'success':
                            domain_creation = result.get('domain_creation')
                            total_visits = result.get('total_visits')

                            if self.is_2025_domain(domain_creation) and total_visits and total_visits != 'None':
                                # 验证流量数据有效性
                                if self.is_valid_traffic_data(total_visits):
                                    traffic_number = self.convert_traffic_to_number(total_visits)
                                    # 只有访问量大于0的才添加
                                    if traffic_number > 0:
                                        # 处理访问量变化字段
                                        visits_change = result.get('visits_change', result.get('change', 'N/A'))
                                        if visits_change is None or visits_change == 'None' or visits_change == '':
                                            visits_change = 'N/A'

                                        domain_info = {
                                            'domain': result.get('domain', 'unknown'),
                                            'totalVisits': total_visits,
                                            'trafficNumber': traffic_number,
                                            'domainCreation': domain_creation,
                                            'visitsChange': visits_change,
                                            'index': result.get('index', 0)
                                        }

                                        new_2025_domains.append(domain_info)
                    
                    # 文件信息
                    file_stat = os.stat(file)
                    file_info.append({
                        'name': os.path.basename(file),
                        'processed': data.get('total_processed', 0),
                        'size': file_stat.st_size,
                        'modified': datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                    })
                    
                except Exception as e:
                    print(f"处理文件 {file} 失败: {e}")
                    continue
            
            # 去重2025年域名
            unique_2025_domains = {}
            for domain_info in new_2025_domains:
                domain = domain_info['domain']
                if domain not in unique_2025_domains:
                    unique_2025_domains[domain] = domain_info
            
            new_2025_domains = list(unique_2025_domains.values())
            
            # 按访问量排序
            new_2025_domains.sort(key=lambda x: x.get('trafficNumber', 0), reverse=True)
            
            # 更新数据
            self.latest_data = {
                'totalProcessed': total_processed,
                'successCount': total_success,
                'trafficFoundCount': total_traffic,
                'dateFoundCount': total_dates,
                'successRate': total_success / max(total_processed, 1) * 100,
                'trafficRate': total_traffic / max(total_processed, 1) * 100,
                'dateRate': total_dates / max(total_processed, 1) * 100,
                'domains2025': new_2025_domains,
                'lastUpdate': datetime.now().isoformat(),
                'files': sorted(file_info, key=lambda x: x['modified'], reverse=True)[:10]
            }
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 数据更新: {total_processed:,} 个域名, {len(new_2025_domains)} 个2025年域名 (调试计数: {debug_2025_count})")
            
        except Exception as e:
            print(f"数据更新失败: {e}")
    
    def continuous_update(self):
        """持续更新数据"""
        while True:
            time.sleep(10)  # 每10秒更新一次
            self.update_data()

# 创建数据收集器
data_collector = RealDataCollector()

@app.route('/')
def index():
    """主页"""
    return send_file('web-monitor/index.html')

@app.route('/api/status')
def get_status():
    """获取当前状态"""
    return jsonify(data_collector.latest_data)

@app.route('/api/domains2025')
def get_2025_domains():
    """获取2025年域名列表"""
    return jsonify({
        'domains': data_collector.latest_data['domains2025'],
        'count': len(data_collector.latest_data['domains2025']),
        'lastUpdate': data_collector.latest_data['lastUpdate']
    })

@app.route('/api/stats')
def get_stats():
    """获取统计信息"""
    data = data_collector.latest_data
    return jsonify({
        'totalProcessed': data['totalProcessed'],
        'successRate': data['successRate'],
        'trafficRate': data['trafficRate'],
        'dateRate': data['dateRate'],
        'domains2025Count': len(data['domains2025']),
        'lastUpdate': data['lastUpdate']
    })

if __name__ == '__main__':
    print("🌐 启动实时监控服务器...")
    print("📱 访问地址: http://localhost:5000")
    print("🔄 API状态: http://localhost:5000/api/status")
    print("🆕 2025年域名: http://localhost:5000/api/domains2025")
    
    app.run(host='0.0.0.0', port=5000, debug=False)
