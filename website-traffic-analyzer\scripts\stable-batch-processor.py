#!/usr/bin/env python3
"""
稳定的批量处理器 - 单进程但稳定可靠
"""

import pandas as pd
import requests
import time
import json
from datetime import datetime
import re
import sys
import os

class StableBatchProcessor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Connection': 'keep-alive'
        })
        self.processed_count = 0
        self.success_count = 0
        self.traffic_found_count = 0
        self.date_found_count = 0
        self.new_2025_domains = []
        
    def extract_traffic_data(self, domain):
        """提取数据"""
        try:
            url = f"https://traffic.cv/{domain}"
            response = self.session.get(url, timeout=20)
            
            if response.status_code != 200:
                return {'domain': domain, 'error': f'HTTP {response.status_code}'}
            
            html = response.text
            
            result = {
                'domain': domain,
                'status': 'success',
                'total_visits': None,
                'domain_creation': None,
                'visits_change': None,
                'html_length': len(html)
            }
            
            if 'Total Visits' in html:
                # 更精确的提取：在Total Visits后面200个字符内查找
                total_visits_pos = html.find('Total Visits')
                search_text = html[total_visits_pos:total_visits_pos + 200]

                # 查找数字（包括0）
                pattern1 = r'>([0-9]+\.?[0-9]*[KMB]?)<'
                match1 = re.search(pattern1, search_text)
                if match1:
                    visits_value = match1.group(1)
                    # 验证数据有效性
                    if self.is_valid_traffic_data(visits_value):
                        result['total_visits'] = visits_value

                    # 查找变化百分比
                    change_match = re.search(r'([+-][0-9]+\.?[0-9]*%)', search_text)
                    if change_match:
                        result['visits_change'] = change_match.group(1)
            
            if 'Domain Creation' in html:
                pattern2 = r'Domain\s+Creation.*?([0-9]{4}-[0-9]{1,2}-[0-9]{1,2})'
                match2 = re.search(pattern2, html, re.IGNORECASE | re.DOTALL)
                if match2:
                    result['domain_creation'] = self.normalize_date(match2.group(1))
            
            return result
            
        except Exception as e:
            return {'domain': domain, 'error': str(e)}
    
    def normalize_date(self, date_str):
        """标准化日期格式"""
        try:
            parts = date_str.split('-')
            if len(parts) == 3:
                year = int(parts[0])
                month = int(parts[1])
                day = int(parts[2])
                return f"{year:04d}-{month:02d}-{day:02d}"
        except:
            pass
        return date_str
    
    def convert_traffic_to_number(self, traffic_str):
        """将流量字符串转换为数字"""
        if not traffic_str:
            return 0
        
        traffic_str = str(traffic_str).upper()
        
        try:
            if traffic_str.endswith('K'):
                return float(traffic_str[:-1]) * 1000
            elif traffic_str.endswith('M'):
                return float(traffic_str[:-1]) * 1000000
            elif traffic_str.endswith('B'):
                return float(traffic_str[:-1]) * 1000000000
            else:
                return float(traffic_str.replace(',', ''))
        except:
            return 0
    
    def is_2025_domain(self, date_str):
        """检查是否是2025年注册的域名"""
        if not date_str:
            return False
        try:
            year = int(date_str.split('-')[0])
            return year == 2025
        except:
            return False

    def is_valid_traffic_data(self, traffic_str):
        """验证流量数据是否有效"""
        if not traffic_str:
            return False

        # 排除明显错误的数据
        if traffic_str in ['0', '9b', '9B', '0K', '0M', '0B']:
            return False

        # 检查是否是单独的整数+单位（可能是错误的）
        if re.match(r'^[0-9][bBmMkK]$', traffic_str):
            return False

        # 包含小数点的更可能是真实数据
        if '.' in traffic_str:
            return True

        # 检查数字是否合理
        try:
            number_part = re.findall(r'([0-9]+)', traffic_str)[0]
            number = int(number_part)

            # 如果是整数且小于等于9，可能是错误数据
            if number <= 9:
                return False

            return True
        except:
            return False
    
    def process_continuous(self, start_index=0, target_count=1000000, delay=0.5):
        """连续处理域名"""
        try:
            print(f"📖 读取Excel文件...")
            df = pd.read_excel('top1M.xlsx')
            total_domains = len(df)
            print(f"📊 Excel文件包含 {total_domains:,} 个域名")
            
            # 计算实际处理范围
            end_index = min(start_index + target_count, total_domains)
            
            print(f"🎯 连续处理域名 {start_index:,} 到 {end_index-1:,}")
            print(f"📊 目标处理数量: {target_count:,}")
            print(f"⏱️ 延迟时间: {delay} 秒")
            print(f"⏱️ 预计耗时: {target_count * delay / 3600:.1f} 小时")
            
            results = []
            start_time = datetime.now()
            last_save_time = start_time
            
            for i in range(target_count):
                current_index = start_index + i
                
                if current_index >= total_domains:
                    print(f"✅ 已处理完所有域名")
                    break
                
                domain_raw = df['domain_name'].iloc[current_index]
                domain = str(domain_raw).strip().rstrip('.')
                
                # 显示进度
                if (i + 1) % 1000 == 0:
                    elapsed = (datetime.now() - start_time).total_seconds()
                    progress = (i + 1) / target_count * 100
                    speed = (i + 1) / elapsed if elapsed > 0 else 0
                    remaining_time = (target_count - i - 1) / speed if speed > 0 else 0
                    
                    print(f"\n🔍 进度: {i+1:,}/{target_count:,} ({progress:.1f}%)")
                    print(f"   当前域名: {domain} (#{current_index:,})")
                    print(f"   处理速度: {speed:.1f} 域名/秒")
                    print(f"   预计剩余: {remaining_time/3600:.1f} 小时")
                    print(f"   成功率: {self.success_count/(i+1)*100:.1f}%")
                    print(f"   2025年域名: {len(self.new_2025_domains)}")
                
                # 提取数据
                result = self.extract_traffic_data(domain)
                result['index'] = current_index
                result['processed_at'] = datetime.now().isoformat()
                
                results.append(result)
                self.processed_count += 1
                
                # 统计成功率
                if result.get('status') == 'success':
                    self.success_count += 1
                    if result.get('total_visits'):
                        self.traffic_found_count += 1
                    if result.get('domain_creation'):
                        self.date_found_count += 1
                        
                        # 检查是否是2025年域名
                        if self.is_2025_domain(result.get('domain_creation')):
                            total_visits = result.get('total_visits')
                            # 确保有真实的访问量数据
                            if total_visits and total_visits != 'None' and total_visits != '0':
                                traffic_num = self.convert_traffic_to_number(total_visits)
                                if traffic_num > 0:  # 访问量大于0
                                    self.new_2025_domains.append({
                                        'domain': domain,
                                        'total_visits': total_visits,
                                        'traffic_number': traffic_num,
                                        'domain_creation': result.get('domain_creation'),
                                        'visits_change': result.get('visits_change', ''),
                                        'index': current_index,
                                        'ranking': current_index + 1
                                    })
                                    print(f"   🆕 发现2025年新域名: {domain} (#{current_index+1:,}) - {total_visits} - {result.get('domain_creation')}")
                
                # 每100个保存一次
                if (i + 1) % 100 == 0:
                    temp_filename = f'stable_batch_{start_index}_{current_index}.json'
                    self.save_results(results, temp_filename)
                    print(f"   💾 临时保存: {temp_filename}")
                    
                    # 显示2025年域名排行
                    if self.new_2025_domains:
                        self.show_2025_ranking()
                
                # 延迟
                time.sleep(delay)
            
            return results
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return results if 'results' in locals() else []
    
    def show_2025_ranking(self):
        """显示2025年域名排行"""
        if not self.new_2025_domains:
            return
        
        sorted_domains = sorted(self.new_2025_domains, key=lambda x: x['traffic_number'], reverse=True)
        
        print(f"\n🆕 2025年新上线网站流量增长榜 (共{len(sorted_domains)}个):")
        print("-" * 90)
        print(f"{'排名':<4} {'域名':<30} {'月访问量':<12} {'注册日期':<12} {'变化':<10} {'原排名'}")
        print("-" * 90)
        
        for i, domain_info in enumerate(sorted_domains[:20], 1):  # 显示前20名
            domain = domain_info['domain']
            visits = domain_info['total_visits']
            creation = domain_info['domain_creation']
            change = domain_info['visits_change']
            ranking = domain_info['ranking']
            print(f"{i:<4} {domain:<30} {visits:<12} {creation:<12} {change:<10} #{ranking:,}")
        
        if len(sorted_domains) > 20:
            print(f"... 还有 {len(sorted_domains) - 20} 个2025年新域名")
    
    def save_results(self, results, filename=None):
        """保存结果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'stable_batch_results_{timestamp}.json'
        
        data = {
            'timestamp': datetime.now().isoformat(),
            'processor': 'stable_batch_processor',
            'total_processed': len(results),
            'success_count': self.success_count,
            'traffic_found_count': self.traffic_found_count,
            'date_found_count': self.date_found_count,
            'success_rate': self.success_count / max(len(results), 1) * 100,
            'traffic_rate': self.traffic_found_count / max(len(results), 1) * 100,
            'date_rate': self.date_found_count / max(len(results), 1) * 100,
            'new_2025_domains_count': len(self.new_2025_domains),
            'new_2025_domains': self.new_2025_domains,
            'results': results
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return filename

def main():
    start_index = int(sys.argv[1]) if len(sys.argv) > 1 else 0
    target_count = int(sys.argv[2]) if len(sys.argv) > 2 else 1000000
    delay = float(sys.argv[3]) if len(sys.argv) > 3 else 0.5
    
    processor = StableBatchProcessor()
    
    print("🔒 稳定批量处理器")
    print("=" * 60)
    print(f"📍 开始位置: {start_index:,}")
    print(f"🎯 目标数量: {target_count:,}")
    print(f"⏱️ 延迟时间: {delay} 秒")
    
    results = processor.process_continuous(start_index, target_count, delay)
    
    if results:
        final_filename = processor.save_results(results)
        
        print(f"\n🎉 处理完成!")
        print(f"📊 总处理: {len(results):,}")
        print(f"✅ 成功率: {processor.success_count/len(results)*100:.1f}%")
        print(f"📈 流量数据: {processor.traffic_found_count}")
        print(f"📅 注册日期: {processor.date_found_count}")
        print(f"🆕 2025年域名: {len(processor.new_2025_domains)}")
        print(f"📁 最终文件: {final_filename}")
        
        # 最终排行榜
        processor.show_2025_ranking()

if __name__ == "__main__":
    main()
