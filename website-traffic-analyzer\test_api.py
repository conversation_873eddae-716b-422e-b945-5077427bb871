#!/usr/bin/env python3
import requests
import json

try:
    response = requests.get('http://localhost:5000/api/status')
    data = response.json()
    
    print("🔍 实时监控API测试结果:")
    print("=" * 50)
    print(f"总处理域名: {data['totalProcessed']:,}")
    print(f"成功率: {data['successRate']:.1f}%")
    print(f"流量发现率: {data['trafficRate']:.1f}%")
    print(f"日期发现率: {data['dateRate']:.1f}%")
    print(f"2025年域名数量: {len(data['domains2025'])}")
    
    print("\n🆕 2025年新域名列表:")
    print("-" * 50)
    for i, domain in enumerate(data['domains2025'][:10], 1):
        print(f"{i:2d}. {domain['domain']} - {domain['totalVisits']} ({domain['domainCreation']})")
    
    print(f"\n📊 数据文件: {len(data['files'])} 个")
    print(f"最后更新: {data['lastUpdate']}")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
