#!/usr/bin/env python3
import requests
import json

try:
    response = requests.get('http://localhost:5000/api/status')
    data = response.json()
    
    print("🔍 前端数据结构测试:")
    print("=" * 60)
    
    domains = data.get('domains2025', [])
    print(f"总共 {len(domains)} 个2025年域名")
    
    print("\n📋 前5个域名的完整数据结构:")
    print("-" * 60)
    
    for i, domain in enumerate(domains[:5], 1):
        print(f"\n{i}. 域名: {domain.get('domain', 'MISSING')}")
        print(f"   月访问量: {domain.get('totalVisits', 'MISSING')}")
        print(f"   注册日期: {domain.get('domainCreation', 'MISSING')}")
        print(f"   访问量变化: {domain.get('visitsChange', 'MISSING')}")
        print(f"   排名: #{domain.get('index', 'MISSING')}")
        print(f"   流量数值: {domain.get('trafficNumber', 'MISSING')}")
        
        # 检查是否有undefined或None值
        issues = []
        for key, value in domain.items():
            if value is None or value == 'None' or value == 'undefined':
                issues.append(f"{key}={value}")
        
        if issues:
            print(f"   ⚠️ 问题字段: {', '.join(issues)}")
        else:
            print(f"   ✅ 数据完整")
    
    print(f"\n📊 数据完整性检查:")
    print("-" * 60)
    
    complete_count = 0
    incomplete_count = 0
    
    for domain in domains:
        required_fields = ['domain', 'totalVisits', 'domainCreation', 'visitsChange', 'index']
        missing_fields = []
        
        for field in required_fields:
            value = domain.get(field)
            if value is None or value == 'None' or value == 'undefined' or value == '':
                missing_fields.append(field)
        
        if missing_fields:
            incomplete_count += 1
            if incomplete_count <= 3:  # 只显示前3个有问题的
                print(f"❌ {domain.get('domain', 'unknown')}: 缺失 {', '.join(missing_fields)}")
        else:
            complete_count += 1
    
    print(f"\n✅ 完整数据: {complete_count} 个")
    print(f"❌ 不完整数据: {incomplete_count} 个")
    print(f"📈 数据完整率: {complete_count/(complete_count+incomplete_count)*100:.1f}%")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
